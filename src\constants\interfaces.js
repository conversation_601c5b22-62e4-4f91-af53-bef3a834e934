// 登录相关接口
export const RENEW_URL = '//passport.iq.com/intl/sso/renew_authcookie.action'

// 收藏
export const COLLECTIONADD =
  '//subscription.iq.com/dingyue/api/subscribe.action'

export const MODE_PTID = '//pcw-api.iq.com/api/conf-id'
// 多语言
export const LANG_PKG = '//pcw-api.iq.com/api/langPkg'

// 套餐查询接口
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
// export const intlVipCheckoutInterface =
//   '//i.vip.iq.com/client/store/i18n/pcw/checkout'
export const intlVipCheckoutInterface =
  '//global.vip.iq.com/vip-global-store/external/pcw/checkout'
// export const newintlVipCheckoutInterface =
//   '//global-store.online.qiyi.qae/client/store/i18n/pcw/ab/checkout'

// 新的套餐查询接口
// export const newintlVipCheckoutInterface =
// '/client/store/i18n/pcw/ab/checkout?user=yyh'
//   // '//viptrade-global-store20220414220631.test.qiyi.qae/client/store/i18n/pcw/ab/checkout'

// export const newintlVipCheckoutInterface =
//   '//i.vip.iq.com/client/store/i18n/pcw/ab/checkout'
export const newintlVipCheckoutInterface =
  '//global.vip.iq.com/vip-global-store/external/pcw/checkout'
// 支付接口
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=564463185
// export const doPayInterface = '//i.vip.iq.com/pay/dopay.action'
export const doPayInterface = '//global.vip.iq.com/vip-global-trade/external/pay/dopay'
// '//vip-global-trade-api20220414220631.test.qiyi.qae/pay/dopay.action'
//'/pay/dopay.action?user=yyh'

// 支付结果查询接口
// http://wiki.qiyi.domain/pages/viewpage.action?pageId=*********
export const intlVipPayResultInterface = //'/usergateway/payresult/i18n/query.action?user=yyh'
  //
  '//i.vip.iq.com/usergateway/payresult/i18n/query.action'

export const vodCheckoutInterface =
  '//i.vip.iq.com/client/store/i18n/mobile/vodCheckout'

export const SUBMIT_PAY_PHONE =
  '//pay.iq.com/pay-product-international/intl/coda/gopay/submitphone'

// 银行卡完成支付接口 https://iq.feishu.cn/wiki/IbjJwRKT3itU10k1I3AcBPkzn6g?open_in_browser=true
export const bankDoPayInterface =
  //'//pay-test.iqiyi.com/pay-product-international/mastercard/m4m/doPay'
  '//pay.iq.com/pay-product-international/mastercard/m4m/doPay'

// 用户信息接口  http://wiki.qiyi.domain/pages/viewpage.action?pageId=**********
export const USER_INFO_URL = '//pcw-api.iq.com/api/pvvp' // test-fapi.iq.com

// export const EXCHANGE_COUPON = '//intl-pcell.qiyi.domain/exchange-code/api/exchange'
// 用户风险等级 http://wiki.qiyi.domain/pages/viewpage.action?pageId=**********
export const USER_RISK = '//i.vip.iq.com/vip-global-coupon/user/risk'
// 优惠券兑换
export const EXCHANGE_COUPON = '//i.vip.iq.com/vip-global-coupon/user/get'

// 优惠券解冻接口 http://wiki.qiyi.domain/pages/viewpage.action?pageId=**********
export const FREEZE_COUPON = '//i.vip.iq.com/vip-global-coupon/user/unfreeze'

// 功能配置：http://pms.qiyi.domain/browse/GLOBALREQ-5321
export const FUNCS_API = '//intl.iqiyi.com/control/feature_switch'

//  万事达3Ds验证流程初始化验证接口  http://wiki.qiyi.domain/pages/viewpage.action?pageId=1372560242 https://iq.feishu.cn/wiki/W5STwxpDuigjLXkURaYcxPUMnsh?open_in_browser=true
export const INIT_3DS_VERIFY = // '//pay-test.iqiyi.com/pay-product-international/mastercard/3ds2/initiate'
  '//pay.iq.com/pay-product-international/mastercard/3ds2/initiate' // '//pay.iq.com/pay-product-international/mastercard/3ds2/initiate'

// 支付结果页（card化版本）query接口 http://wiki.qiyi.domain/pages/viewpage.action?pageId=1451065521
// export const QUERY_CARD = '//i.vip.iq.com/payresult/i18n/queryV2'
export const QUERY_CARD = '//global.vip.iq.com/vip-global-payresult/external/card/queryV2'

// 营销接口文档 http://wiki.qiyi.domain/pages/viewpage.action?pageId=1478788658
export const STAY_USER =
  '//api.iq.com/activity/cashier/retention/entry'

// 代金券领用接口 http://wiki.qiyi.domain/pages/viewpage.action?pageId=1478790715
export const RESULT_COUPON = '//i.vip.iq.com/vip-global-coupon/user/payResultCoupon'

// 用户绑卡信息 https://iq.feishu.cn/wiki/Y5vFw7xY5iAcWEk7gsDcYspCnAc
// export const USER_BOUND_CARD = '//pay-test.iqiyi.com/pay-product-international/mastercard/m4m/queryCardInfos'
export const USER_BOUND_CARD = '//pay.iq.com/pay-product-international/mastercard/m4m/queryCardInfos'
