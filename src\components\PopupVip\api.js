import qs from 'qs'
import {
  vodCheckoutInterface,
  intlVipCheckoutInterface,
  newintlVipCheckoutInterface,
  doPayInterface,
  USER_INFO_URL,
  USER_BOUND_CARD,
  STAY_USER
} from '@/constants/interfaces'
import $http from '@/kit/fetch'
import { getCookies, setCookies } from '@/kit/cookie'
import { isLogin, getUid } from '@/utils/userInfo'
import { getTimeZone, isEmptyStr } from '@/utils/common'
import { getDevice, iqSwitchPlatformId } from '@/kit/device'
import { rebuildCommonUrl, locUrl, platformId } from '@/kit/common'
import { queryFromUrl } from '@/kit/url'
import { thirdParty } from './config'
export const getVodData = async (params, lang, mod) => {
  const res = await $http(vodCheckoutInterface, {
    timeout: 5000,
    method: 'POST',
    credentials: true,
    params: qs.stringify({
      aid: params.aid,
      pid: params.pid,
      platform: params.bossCode,
      lang,
      app_lm: mod || 'my',
      timeZone: getTimeZone(mod),
      device_id: getCookies('QC005'),
      packageVersion: '1.0',
      payTypeVersion: '1.0',
      clientVersion: '2.10.0',
      payAutoRenew: params.payAutoRenew,
      vipType: params.vipType,
      fv: params.fv,
      fc: params.fc,
      fr_version: params.fr_version
    })
  })
  if (res.code === 'A00000') {
    const { product } = res.data
    const packageInfo = res.data.packageInfo[0]
    return {
      product,
      packageInfo: {
        ...packageInfo,
        vodPrice: packageInfo.vodPrice,
        payTypeOptions: packageInfo.payTypeOptions.sort(
          (a, b) => a.sort - b.sort
        )
      },
      _abtest: res.data.abtest || ''
    }
  } else {
    throw new Error(res.message || res.msg || 'network err')
  }
}
export const getVipData = async (params = {}, lang, mod) => {
  // const { mod, lang } = modeLangObj
  const vipInfoRes = await $http(intlVipCheckoutInterface, {
    method: 'POST',
    credentials: true,
    params: qs.stringify({
      vipType: params.vipType || '',
      amount: params.amount || '',
      albumId: params.albumId || '',
      fv: params.fv || '',
      fc: params.fc || '',
      fr_version: params.fr_version || '',
      platform: params.bossCode,
      version: '1.0',
      payTypeVersion: '1.0',
      lang,
      app_lm: mod || 'my',
      timeZone: getTimeZone(mod),
      payAutoRenew: params.payAutoRenew || '',
      device_id: getCookies('QC005')
    })
  })
  if (vipInfoRes.code === 'A00000') {
    return vipInfoRes.data
  } else {
    throw new Error(vipInfoRes.msg || vipInfoRes.message || '')
  }
}

export const newgetVipData = async (params = {}, lang, mod) => {
  // console.log('in api getvipdata*********')
  // const { mod, lang } = modeLangObj
  let vipInfoRes = null
  try {
    vipInfoRes = await $http(newintlVipCheckoutInterface, {
      method: 'POST',
      timeout: 5000,
      credentials: true,
      params: qs.stringify({
        vipType: params.vipType || '',
        amount: params.amount || '',
        // albumId: params.albumId || '',
        fv: params.fv || '',
        fc: params.fc || '',
        // fr_version: params.fr_version || '',
        platform: params.bossCode,
        // version: '1.0',
        // payTypeVersion: '1.0',
        lang,
        app_lm: mod || 'intl',
        // timeZone: getTimeZone(mod),
        payAutoRenew: params.payAutoRenew || '',
        // clientVersion: '3.10.0',
        // bizSource: 'GPhone'
        device_id: getCookies('QC005')
      })
    })
    // return res.data
    // let vipInfoRes = resData

    return vipInfoRes
  } catch (err) {
    throw new Error(err)
  }
}

export const handleVipInfo = (data, vipLangPkg) => {
  const keys = Object.keys(data.storeNodeLocations)[0]
  const oneStoreNodeLocations = data.storeNodeLocations[keys]
  const isPopupMixed = oneStoreNodeLocations.isPopupMixed

  const vipTextNodes = {
    firstStepHeader:
      (oneStoreNodeLocations.firstStepHeader &&
        oneStoreNodeLocations.firstStepHeader.text) ||
      '',
    firstStepTip:
      (oneStoreNodeLocations.firstStepTip &&
        oneStoreNodeLocations.firstStepTip.text) ||
      '',
    vipRedeemCodeLocation:
      oneStoreNodeLocations.vipRedeemCodeLocation &&
      oneStoreNodeLocations.vipRedeemCodeLocation.text,
    firstStepBottomLine:
      oneStoreNodeLocations.firstStepBottomLine &&
      oneStoreNodeLocations.firstStepBottomLine.text,
    secondStepHeader:
      (oneStoreNodeLocations.secondStepHeader &&
        oneStoreNodeLocations.secondStepHeader.text) ||
      '',
    secondStepTip:
      (oneStoreNodeLocations.secondStepTip &&
        oneStoreNodeLocations.secondStepTip.text) ||
      '',
    autorenewTermsAndConditions:
      oneStoreNodeLocations.autorenewTermsAndConditions,
    vipServiceAgreement: oneStoreNodeLocations.vipServiceAgreement
  }

  const vipTypes = oneStoreNodeLocations.vipTypeNameLocation.map((type, i) => {
    const typeId = Number(oneStoreNodeLocations.vipTypeIdLocation[i].text)
    return {
      id: typeId,
      name: type.text
    }
  })

  const vipPkgList = []
    .concat(data.autoRenewSelectMonths, data.selectMonthes)
    .sort((a, b) => a.sort - b.sort)
    .map((item, i) => {
      const type = vipTypes.find((type) => type.id === item.vipTypeId)
      return {
        ...item,
        vipTypeName: type.name,
        storeNodeLocations: data.storeNodeLocations[item.vipTypeId],
        detail: getDetailText(item, vipLangPkg),
        index: i
      }
    })

  return {
    ...data,
    vipPkgList,
    vipTypes,
    isPopupMixed,
    ...vipTextNodes
  }
}

export const newHandleVipInfo = (
  vipInfo,
  vipLangPkg,
  hasCoupon = true,
  couponCode,
  hasCard
) => {
  // 对数据进行第一次处理
  const {
    abCard = {},
    productSetCard = [],
    privilegeCard = [],
    agreementCard = []
  } = vipInfo
  // console.log(, 'in new  handle vip data')
  const { vipTypeNameLocation = [], groupCode } = abCard
  const tabNameList = {}
  vipTypeNameLocation.map((item) => {
    tabNameList[item.vipTag] = item.name
  })
  let pageDefaultVipTag = abCard.vipTag || vipTypeNameLocation[0].vipTag
  // 当混版套餐的时候重新查找默认选中的viptag
  if (pageDefaultVipTag === 11) {
    let _findDefault = []
    productSetCard.map((item) => {
      _findDefault
        .concat(item.selectMonthes, item.autoRenewSelectMonths)
        .filter((pkgItem) => {
          return pkgItem.recommend === 1
        })
    })
    pageDefaultVipTag =
      _findDefault.length > 0
        ? _findDefault[0].vipTag
        : vipTypeNameLocation[0].vipTag
  }

  let productsList = {}
  // ？这里有啥用？
  let plainProds = []
  let priList = {}
  let _couponList = []
  let couponCodeList = []
  let couponList = []
  let agreeList = {}
  let cheapRank = {}
  // 最便宜的单个套餐
  let couponDefaultProd = {}
  // 混版套餐
  productSetCard.map((item) => {
    plainProds = plainProds.concat(
      item.selectMonthes,
      item.autoRenewSelectMonths
    )
    productsList[item.vipTag] = []
      .concat(item.selectMonthes, item.autoRenewSelectMonths)
      .sort((a, b) => a.sort - b.sort)
      .map((pItem, i) => {
        const { payTypeOptions } = pItem
        const {
          promotionShow,
          countDown,
          ensureButtonDesc,
          discountMode,
          discountValue = '',
          discountPercent = '',
          needPayFee,
          originPrice
        } = payTypeOptions[0] || {}
        if (
          countDown !== 0 &&
          promotionShow &&
          ensureButtonDesc &&
          discountMode &&
          (!isEmptyStr(discountValue) || !isEmptyStr(discountPercent)) &&
          needPayFee < originPrice
        ) {
          pItem.showDiscountPop = true
        }
        pItem.discountValue = discountValue
        pItem.discountMode = discountMode
        pItem.discountPercent = discountPercent
        pItem.vipTypeName = tabNameList[pItem.vipTag]
        pItem.detail = getDetailText(pItem, vipLangPkg)
        pItem.index = i
        return { ...pItem, tabName: tabNameList[pItem.vipTag] }
      })
    if (item.uniqueCoupons) {
      _couponList = _couponList.concat(item.uniqueCoupons)
    }
  })

  // 去重和去掉非满减券
  _couponList.length > 0 &&
    _couponList.map((item) => {
      item.showDetail = false
      if (
        couponCodeList.indexOf(item.couponCode) < 0 &&
        item.couponType === 1
      ) {
        couponList.push(item)
        couponCodeList.push(item.couponCode)
      }
    })
  productSetCard.map((setCardItem) => {
    let priceProds = []
    let _prods = []
      .concat(setCardItem.selectMonthes, setCardItem.autoRenewSelectMonths)
      .sort((a, b) => a.sort - b.sort)

    let _recommendItem =
      _prods.find((_prodsitem) => _prodsitem.recommend === 1) || {}
    // console.log(_recommendItem, '-----能找到吗？s')
    _prods.map((pkgItem, index) => {
      pkgItem &&
        pkgItem.payTypeOptions.map((payItem) => {
          const { payType } = payItem
          if (true) {
            if (payType === 10010 || payType === 10009) {
              payItem.recommend = 1
            } else {
              payItem.recommend = 0
            }
          }
          // pay
          // console.log('走到这里循环，', payItem.payType)
          if (hasCoupon && payItem.coupons && payItem.coupons.length > 0) {
            payItem.coupons.map((cItem) => {
              let cIndex = couponCodeList.indexOf(cItem.couponCode)
              let couponItem = couponList[cIndex]
              let payPriceProd = {
                price: payItem.needPayFee - couponItem.discountPrice,
                productSetCode: pkgItem.productSetCode,
                payType: payItem.payType,
                couponCode: cItem.couponCode,
                pkgItem,
                payItem,
                couponItem,
                canuseCoupon: true,
                vipTag: setCardItem.vipTag,
                pkgVipTag: pkgItem.vipTag
              }
              priceProds.push(payPriceProd)
            })
          } else {
            let payPriceProd = {
              price: payItem.needPayFee,
              productSetCode: pkgItem.productSetCode,
              payType: payItem.payType,
              couponCode: '',
              pkgItem,
              payItem,
              vipTag: setCardItem.vipTag,
              pkgVipTag: pkgItem.vipTag,
              couponItem: {},
              canuseCoupon: false
            }
            priceProds.push(payPriceProd)
          }
        })
    })
    // 如果传入了优惠券码
    if (couponCode) {
      // 进行价格排序
      // priceProds.sort((a, b) => {
      //   if (a.price === b.price) {
      //     return a.pkgItem.sort - b.pkgItem.sort
      //   }
      //   return a.price - b.price
      // })
      let couponPkgArr = priceProds
        .filter((item) => {
          return item.pkgVipTag === pageDefaultVipTag
        })
        .filter((item) => {
          return item.couponCode === couponCode
        })
        .sort((a, b) => {
          if (a.recommend === b.recommend) {
            if (a.price === b.price) {
              if (a.pkgItem.sort === b.pkgItem.sort) {
                return a.payItem.sort - b.payItem.sort
              }
              return a.pkgItem.sort - b.pkgItem.sort
            }
            return a.price - b.price
          }
          return b.recommend - a.recommend
        })

      if (couponPkgArr.length > 0) {
        if (couponDefaultProd.price) {
          couponDefaultProd =
            couponDefaultProd.price < couponPkgArr[0].price
              ? couponDefaultProd
              : couponPkgArr[0]
        } else {
          couponDefaultProd = couponPkgArr[0] || {}
        }
      } else {
        let noCouponList = priceProds.filter((item) => {
          return item.pkgVipTag === pageDefaultVipTag
        })
        if (noCouponList.length > 0) {
          couponDefaultProd = noCouponList[0]
        } else {
          couponDefaultProd = couponDefaultProd
        }
      }
    } else {
      // 如果没有默认优惠券
      // console.log(priceProds, '000000----00000')
      let _recommendList = priceProds
        .filter((item) => {
          // console.log('么一个欧算一算', item)
          return (
            item.pkgItem.recommend === 1 &&
            item.pkgItem.productSetCode === _recommendItem.productSetCode
          )
        })
        .sort((a, b) => {
          // 先价格排序
          if (a.price === b.price) {
            if (a.couponItem.couponEndTime === b.couponItem.couponEndTime) {
              return a.payItem.sort - b.payItem.sort
            }
            return a.couponItem.couponEndTime - b.couponItem.couponEndTime
          }
          return a.price - b.price
        })

      priceProds = [].concat(
        _recommendList,
        priceProds
          .filter(
            (_aItem) => _aItem.productSetCode !== _recommendItem.productSetCode
          )
          .sort((a, b) => {
            if (a.price === b.price) {
              if (a.couponItem.couponEndTime === b.couponItem.couponEndTime) {
                return a.payItem.sort - b.payItem.sort
              }
              return a.couponItem.couponEndTime - b.couponItem.couponEndTime
            }
            return a.price - b.price
          })
      )
    }
    cheapRank[setCardItem.vipTag] = priceProds
    return cheapRank
  })
  // console.log(cheapRank, '-----=====-----88888888')
  privilegeCard &&
    privilegeCard.map((item) => {
      priList[item.vipTag] = item
    })
  agreementCard &&
    agreementCard.map((item) => {
      agreeList = item
    })

  vipInfo = {
    ...vipInfo,
    productsList,
    priList,
    couponList,
    agreeList,
    plainProds,
    cheapRank,
    defaultCode: couponCode,
    couponDefaultProd,
    groupCode
  }
  let _abtest = getCookies('abtest') || ''
  let abtestObj = _abtest ? JSON.parse(_abtest) : {}
  abtestObj.vipAbtest = groupCode
  setCookies('abtest', JSON.stringify(abtestObj))
  return vipInfo
}

export const getDetailText = (item, vipLangPkg) => {
  let text = ''
  const mod = getCookies('mod')
  const priceStr = mod === 'vn' ? '%p%c' : '%c%p'

  const priceAfterIntro = vipLangPkg.pcashier_plan_priceAfterIntro || ''
  const nextPrice = vipLangPkg.pcashier_plan_nextPrice || ''
  const averagePrice = vipLangPkg.pcashier_plan_averagePrice || ''

  const originalPrice = Number((item.originalPrice / 100).toFixed(2)).toString()
  const averageFee = Number((item.needPayFee / 12 / 100).toFixed(2)).toString()
  if (item.payAutoRenew) {
    if (item.needPayFee < item.originalPrice) {
      text = priceAfterIntro
        .replace('%s', priceStr)
        .replace('%c', item.currencySymbol)
        .replace('%p', originalPrice)
    } else {
      text = nextPrice
        .replace('%s', priceStr)
        .replace('%c', item.currencySymbol)
        .replace('%p', originalPrice)
    }
  }
  if (item.type === 3) {
    text += averagePrice
      .replace('%s', priceStr)
      .replace('%c', item.currencySymbol)
      .replace('%p', averageFee)
  }
  return text
}
export const createOrder = async (data, lang, mod) => {
  const isThirdParty =
    Object.keys(thirdParty).map(Number).indexOf(Number(data.payType)) !== -1
  const dfp = (window.dfp && window.dfp.tryGetFingerPrint()) || ''
  let dId = getCookies('QC005') || dfp
  if (dId === 'null') dId = dfp
  const params = qs.stringify({
    pid: data.pid,
    payType: data.payType,
    amount: data.amount,
    platform: data.bossCode,
    payAutoRenew: data.payAutoRenew || '',
    aid: data.aid || '',
    device_id: dId,
    fc: data.fc || '',
    fv: data.fv || '',
    lang: lang,
    app_lm: mod,
    returnUrl: decodeURIComponent(data.returnUrl || '') || `https://www.iq.com/vip/payResult?cashierType=${data.cashierType || '2'}${data.isLiveTvod ? '&fc=889ecddd9e3af059' : ''}`,
    client_code: getDevice() === 'mobile' ? 'PC_WEB' : 'WN_W_Q',
    t: new Date().getTime(),
    couponCode: data.couponCode,
    fr_version:
      (data.fr_version || '') +
      `cashierType=popup&FromCasher=1&d=${dId}&dfp=${dfp}&fc=${data.fc}${
        data.partnerID ? `&partnerID=${data.partnerID}` : ''
      }${data.fv_abtest ? `&fv_abtest=${data.fv_abtest}` : ''}${
        data.abtest ? `&abtest=${data.abtest}` : ''
      }${data.albumId ? `&aid=${data.albumId}` : ''}`
  })
  // console.log(params, '=======999999*******')
  try {
    const res = await $http(doPayInterface, {
      timeout: 600000,
      credentials: true,
      method: 'POST',
      params
    })
    if (res.code === 'A00000') {
      const data = res.data
      return {
        vipOrder: data.orderCode,
        order: data.order_code,
        redirectUrl: data.redirectUrl
      }
    } else if (res.code === 'COUPON_STATUS_FROZEN') {
      return res
    } else {
      throw new Error(res.msg)
    }
  } catch (error) {
    throw new Error(error)
  }
}
// 生成订单跳转到支付结果页
export const getPayRediectUrl = (params, isLiveTvod) => {
  let _url =
    'vip/bankpay?vipOrder=' +
    params.vipOrder +
    '&order=' +
    params.order +
    '&vipTypeName=' +
    encodeURIComponent(params.typeName || '') +
    '&currencySymbol=' +
    params.currencySymbol +
    '&price=' +
    params.price +
    '&originalPrice=' +
    params.originalPrice +
    '&autorenewTip=' +
    encodeURIComponent(params.autorenewTip || '') +
    '&detail=' +
    encodeURIComponent(params.detail || '') +
    '&text3=' +
    encodeURIComponent(params.name || '') +
    '&cashierType=' +
    encodeURIComponent(params.cashierType || '')
  if (isLiveTvod) {
    _url = _url + '&fc=889ecddd9e3af059'
  }
  return rebuildCommonUrl(_url)
}

export const getMobileUrl = (orderInfo, selectedPkg, payType, inputInfo) => {
  const {
    vipTypeName,
    currencySymbol,
    price,
    originalPrice,
    autorenewTip,
    detail,
    text3,
    id
  } = selectedPkg

  const { vipOrder, redirectUrl } = orderInfo

  if (redirectUrl.match(/vip\/usermobile/)) {
    const url = locUrl()
    const orderParamsQuery = getOrderQuery(url)
    const mobile = queryFromUrl(redirectUrl, 'mobile') || ''
    const order = queryFromUrl(redirectUrl, 'order_code') || ''
    // const mobile = 'MTIzNjU0Nzg4'

    return rebuildCommonUrl(
      `vip/usermobile${
        orderParamsQuery ? orderParamsQuery + '&' : '?'
      }vipOrder=${vipOrder}&order=${order}&vipTypeName=${vipTypeName}&currencySymbol=${currencySymbol}&price=${price}&originalPrice=${originalPrice}&autorenewTip=${autorenewTip}&desc=${detail}&text3=${text3}&mobile=${mobile}&payTypeIcon=${encodeURIComponent(
        payType.iconUrl
      )}&payTypeName=${payType.name}&selectedId=${id}`
    )
  }
  return redirectUrl
}

export const getAbtest = () => {
  // h5: 单点+会员不走ab测试，强制走落地页支付逻辑@王昊
  if (getDevice() === 'mobile') {
    return ''
  }
  const cookieValue = getCookies('QC005')
  const last = cookieValue && cookieValue[cookieValue.length - 1]
  let jumpTest = true
  if (Number(last)) {
    jumpTest = last < 5
  } else {
    jumpTest = last < 'd'
  }
  // return jumpTest ? 'jump_test_a' : 'jump_test_b'
  return jumpTest ? 'popup_test_a' : 'popup_test_b'
}

export const getOrderQuery = (url) => {
  const fvParam = getCookies('playFV') || queryFromUrl(url, 'fv') || ''
  const params = {
    vipType: queryFromUrl(url, 'vipType') || '',
    aid: queryFromUrl(url, 'aid') || '',
    fc: queryFromUrl(url, 'fc') || '',
    fv: fvParam,
    amount: queryFromUrl(url, 'amount') || '',
    payAutoRenew: queryFromUrl(url, 'payAutoRenew') || '',
    albumId: queryFromUrl(url, 'albumId') || '',
    fr_version: queryFromUrl(url, 'fr') || '',
    abtest: queryFromUrl(url, 'abtest') || '',
    partnerID: queryFromUrl(url, 'partnerID') || ''
  }
  let query = ''
  for (const key in params) {
    if (params[key]) {
      if (query) {
        query += `&${key}=${params[key]}`
      } else {
        query += `?${key}=${params[key]}`
      }
    }
  }
  return query
}

// 获取用户信息
export const getUserInfo = async () => {
  const mod = getCookies('mod') || 'intl'
  const lang = getCookies('lang') || 'en_us'
  const params = {
    platformId: platformId(),
    modeCode: mod,
    langCode: lang,
    deviceId: getCookies('QC005'),
    fields: 'userinfo',
    version: '1.0'
  }
  try {
    const res = await $http(USER_INFO_URL, {
      credentials: true,
      method: 'GET',
      params
    })
    if (res.code === '0') {
      return res.data && res.data.userinfo
    } else {
    }
  } catch (err) {
    // 静默处理错误
  }
}

// 获取用户银行卡信息
export const getUserCard = async () => {
  const options = { timeout: 40000, credentials: true }
  const params = {
    sign: 'PCW',
    userId: getUid(),// '2208108010', //
    authcookie: 'PCW'
  }
  options.params = params
  try {
    const resData = await $http(USER_BOUND_CARD, options)
    return resData
  } catch (err) {
    // 静默处理银行卡信息获取失败
  }
}

// 会话级别的展示控制
const RETENTION_SHOWN_KEY = 'cashier_retention_shown'

export const hasShownRetentionInSession = () => {
  return sessionStorage.getItem(RETENTION_SHOWN_KEY) === 'true'
}

export const markRetentionAsShown = () => {
  sessionStorage.setItem(RETENTION_SHOWN_KEY, 'true')
}

export const clearRetentionSession = () => {
  sessionStorage.removeItem(RETENTION_SHOWN_KEY)
}

// 改进的挽留弹窗接口调用
export const getRetentionData = async (lang, mod) => {
  // 检查会话级别的展示控制
  if (hasShownRetentionInSession()) {
    return null
  }

  const params = {
    platform_id: iqSwitchPlatformId(),
    lang,
    app_lm: mod,
    psp_uid: getUid(),
    app_v: '3.1.5',
    app_k: 'appk_pcw',
    dev_os: 'pcw'
  }

  try {
    const resData = await $http(STAY_USER, { params })

    if (resData.code === 0 && resData.data) {
      // 标记为已展示
      markRetentionAsShown()

      // 解析返回数据，适配新的数据结构
      const parsedData = parseRetentionData(resData.data)
      return parsedData
    }
    return null
  } catch (error) {
    console.error('获取挽留弹窗数据失败:', error)
    return null
  }
}

// 解析挽留弹窗数据，适配新的数据结构
const parseRetentionData = (rawData) => {
  const { coverDetail, block, fc, rseat } = rawData

  if (!coverDetail) {
    return null
  }

  const {
    backgroundPic,
    contentPostPicA,
    contentPostPicB,
    contentPostPicC,
    buttonText,
    titleText,
    trans_buttonText,
    linkType,
    linkType2
  } = coverDetail

  // 解析背景图片（支持多种格式）
  let backgroundImages = []

  // 优先使用backgroundPic
  if (backgroundPic) {
    backgroundImages = [backgroundPic]
  }
  // 如果没有backgroundPic，尝试使用contentPostPic系列
  else if (contentPostPicA || contentPostPicB || contentPostPicC) {
    backgroundImages = [contentPostPicA, contentPostPicB, contentPostPicC]
      .filter(pic => pic && pic.trim()) // 过滤空值
  }

  // 解析打点相关的code
  const blockParts = (block || '').split('_')
  const positionCode = blockParts[0] || ''
  const strategyCode = blockParts[1] || ''
  const floatCode = blockParts[2] || ''

  return {
    // 背景图片数组（支持多图）
    backgroundImages,

    // 挽留文案（可选）
    retentionText: titleText || '',

    // 按钮文案（必填）
    stayButtonText: buttonText || '留下',
    leaveButtonText: trans_buttonText || '离开',

    // 打点相关参数
    positionCode,
    strategyCode,
    floatCode,
    block,
    rseat,
    fc,

    // 按钮行为配置
    stayAction: linkType,
    leaveAction: linkType2
  }
}






